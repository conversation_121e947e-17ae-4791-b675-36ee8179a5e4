package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.utils.ResourceReader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DeepLinkingController {

    private final ResourceReader resourceReader;

    public DeepLinkingController(ResourceReader resourceReader) {
        this.resourceReader = resourceReader;
    }

    @GetMapping(path = "/apple-app-site-association", produces = "application/json")
    public String appleAppSiteAssociationFile() {
        return resourceReader.readResource("appStoreConnect/apple-app-site-association");
    }

    @GetMapping(path = "/.well-known/assetlinks.json", produces = "application/json")
    public String googleAssetLinksJson() {
        return resourceReader.readResource("googlePlay/assetlinks.json");
    }
}
