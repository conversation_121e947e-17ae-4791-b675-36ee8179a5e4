package com.arrivinginhighheels.visited.backend.service.translations;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.repository.translations.SupportedLanguageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

@Service
public abstract class AbstractTranslationService {

    @Autowired
    private SupportedLanguageRepository supportedLanguageRepository;

    public SupportedLanguage getCurrentLanguage() {
        var langCode = getCurrentLanguageCode();

        if (langCode == null || langCode.isEmpty()) {
            langCode = SupportedLanguage.ENGLISH_CODE;
        } else if (langCode.equalsIgnoreCase("zh")) {
            langCode = processChinese();
        }

        return supportedLanguageRepository.findByCode(langCode);
    }

    public boolean isInEnglish() {
        var langCode = getCurrentLanguageCode();
        return langCode == null ||
                langCode.isEmpty() ||
                langCode.equalsIgnoreCase(SupportedLanguage.ENGLISH_CODE);
    }

    public String getCurrentLanguageCode() {
        final var locale = LocaleContextHolder.getLocale();
        return locale.getLanguage();
    }

    private String processChinese() {
        var locale = LocaleContextHolder.getLocale();
        final var script = locale.getScript();
        if (script != null && script.equalsIgnoreCase("hant")) {
            return "zh-Hant";
        }

        return "zh-Hans";
    }

    public SupportedLanguage findByCode(String languageCode) {
        final var language = supportedLanguageRepository.findByCode(languageCode);
        if (language == null) {
            return supportedLanguageRepository.findByCode(SupportedLanguage.ENGLISH_CODE);
        }

        return language;
    }
}


