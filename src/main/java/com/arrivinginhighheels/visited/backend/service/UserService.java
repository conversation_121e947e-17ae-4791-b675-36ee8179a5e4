package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.config.Constants.Counters;
import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.exception.EmailAlreadyExistsException;
import com.arrivinginhighheels.visited.backend.exception.InsufficientParametersException;
import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.features.authentication.SessionService;
import com.arrivinginhighheels.visited.backend.features.cities.UserCitiesRepository;
import com.arrivinginhighheels.visited.backend.features.emails.EmailService;
import com.arrivinginhighheels.visited.backend.features.experiences.UserExperienceAreaRepository;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.features.inspirations.UserInspirationsRepository;
import com.arrivinginhighheels.visited.backend.features.itineraries.UserItineraryRepository;
import com.arrivinginhighheels.visited.backend.features.poster.PrintingRepository;
import com.arrivinginhighheels.visited.backend.features.poster.PrintingTranslationService;
import com.arrivinginhighheels.visited.backend.features.privacy.PrivacyService;
import com.arrivinginhighheels.visited.backend.features.todoLists.UserTodoListItemRepository;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.*;
import com.arrivinginhighheels.visited.backend.security.JwtTokenUtil;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.utils.ResourceReader;
import jakarta.validation.Valid;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.logging.Logger;

/**
 * Business logic class for the users
 */
@Service
public class UserService {

    private static final Logger log = Logger.getLogger("UserService");
    private final UserRepository userRepository;
    private final UserBuilder userBuilder;
    private final CounterRepository counterRepository;
    private final PrivacyService privacyService;
    private final SelectionRepository selectionRepository;
    private final UserBeenCountRepository userBeenCountRepository;
    private final UserDeletionQueueRepository userDeletionQueueRepository;
    private final SessionRepository sessionRepository;
    private final GeoAreaUserNotesRepository geoAreaUserNotesRepository;
    private final UserExperienceAreaRepository userExperienceAreaRepository;
    private final UserScoresRepository userScoresRepository;
    private final UserPurchasesRepository userPurchasesRepository;
    private final UserCitiesRepository userCitiesRepository;
    private final UserInspirationsRepository userInspirationsRepository;
    private final UserTodoListItemRepository userTodoListItemRepository;
    private final PrintingRepository printingRepository;
    private final PasswordEncoder passwordEncoder;
    private final UserItineraryRepository userItineraryRepository;
    private final EmailService emailService;
    private final YamlConfig config;
    private final PrintingTranslationService translationService;
    private final SessionService sessionService;
    private final JwtTokenUtil jwtTokenUtil;
    private final ResourceReader resourceReader;

    public UserService(
            UserDeletionQueueRepository userDeletionQueueRepository,
            UserRepository userRepository,
            UserBuilder userBuilder,
            UserScoresRepository userScoresRepository,
            UserInspirationsRepository userInspirationsRepository,
            CounterRepository counterRepository,
            PrivacyService privacyService,
            SelectionRepository selectionRepository,
            UserBeenCountRepository userBeenCountRepository,
            SessionRepository sessionRepository,
            PasswordEncoder passwordEncoder,
            GeoAreaUserNotesRepository geoAreaUserNotesRepository,
            UserTodoListItemRepository userTodoListItemRepository,
            PrintingRepository printingRepository,
            UserPurchasesRepository userPurchasesRepository,
            UserCitiesRepository userCitiesRepository,
            UserExperienceAreaRepository userExperienceAreaRepository,
            UserItineraryRepository userItineraryRepository,
            EmailService emailService,
            YamlConfig config,
            PrintingTranslationService translationService,
            SessionService sessionService, JwtTokenUtil jwtTokenUtil, ResourceReader resourceReader
    ) {
        this.userDeletionQueueRepository = userDeletionQueueRepository;
        this.userRepository = userRepository;
        this.userBuilder = userBuilder;
        this.userScoresRepository = userScoresRepository;
        this.userInspirationsRepository = userInspirationsRepository;
        this.counterRepository = counterRepository;
        this.privacyService = privacyService;
        this.selectionRepository = selectionRepository;
        this.userBeenCountRepository = userBeenCountRepository;
        this.sessionRepository = sessionRepository;
        this.passwordEncoder = passwordEncoder;
        this.geoAreaUserNotesRepository = geoAreaUserNotesRepository;
        this.userTodoListItemRepository = userTodoListItemRepository;
        this.printingRepository = printingRepository;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userCitiesRepository = userCitiesRepository;
        this.userExperienceAreaRepository = userExperienceAreaRepository;
        this.userItineraryRepository = userItineraryRepository;
        this.emailService = emailService;
        this.config = config;
        this.resourceReader = resourceReader;
        this.translationService = translationService;
        this.sessionService = sessionService;
        this.jwtTokenUtil = jwtTokenUtil;
    }

    @Transactional
    public User createUser(final AuthRequest userDTO) {

        var email = userDTO.getEmail();
        if (email != null) {
            email = email.toLowerCase();
        }

        if (userRepository.findByUsername(email).isPresent()) {
            throw new EmailAlreadyExistsException();
        }

        final Counter totalUsersCounter = getTotalUsersCounter();
        final long nextTotalUsers = totalUsersCounter.getValue() + 1;

        final var user = userBuilder.createUserFromDTO(userDTO);
        user.setUsername(email);
        user.setRanking(nextTotalUsers);
        user.setNumberOfAreasVisited(0);
        user.setCreationDate(new Date());

        if (userDTO.getPassword() != null) {
            var encoded = passwordEncoder.encode(userDTO.getPassword());
            user.setPassword(encoded);
        }

        final var userSaved = userRepository.save(user);

        totalUsersCounter.setValue(nextTotalUsers);
        counterRepository.save(totalUsersCounter);

        return userSaved;
    }

    public User findUser(final String username) {
        if (username == null) {
            throw new InsufficientParametersException("id");
        }


        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UserNotFoundByUsernameException(username));
    }

    @Transactional
    public void recalculateUsersTotalNumberOfAreasVisited(final User user) {
        final Integer totalNumberOfAreasVisited = userRepository.getTotalNumberOfAreasVisitedForTheUser(user.getId());

        user.setNumberOfAreasVisited(totalNumberOfAreasVisited);

        log.info("Setting the total number of areas visited for user " + user.getUsername() + " to " +
                totalNumberOfAreasVisited);

        userRepository.saveAndFlush(user);
    }

    @Transactional
    public Long calculateRankForTheUser(final User user) {
        log.info("Calculating the ranking for the user " + user.getUsername());
        final Long rankForTheUser = userRepository.getRankForTheUser(user.getId());
        user.setRanking(rankForTheUser);
        userRepository.save(user);

        return user.getRanking();
    }

    @Transactional
    public Long countUsers() {
        final Counter totalUsersCounter = getTotalUsersCounter();

        return totalUsersCounter.getValue();
    }

    private Counter getTotalUsersCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        if (totalUsersCounter == null) {
            final Long totalUsers = userRepository.count();
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
            counterRepository.save(totalUsersCounter);
        }

        return totalUsersCounter;
    }

    @Transactional
    public void deleteUser(final User user) {

        //remove all data relative to the user in the database
        userExperienceAreaRepository.deleteByUser(user);
        userBeenCountRepository.deleteByUser(user);
        userScoresRepository.deleteByUserId(user.getId());
        userPurchasesRepository.deleteByUser(user);
        userTodoListItemRepository.deleteByUser(user);
        userCitiesRepository.deleteByUser(user);
        userInspirationsRepository.deleteByUser(user);
        privacyService.deletePrivacyAgreementForUserId(user);
        printingRepository.deleteByUser(user);
        userItineraryRepository.deleteAllByUser(user);

        try {
            selectionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            sessionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            geoAreaUserNotesRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        //save the user deletion in the queue to remove dirty data from the Selection Logs later
        final UserDeletionQueue userDeletionQueue = new UserDeletionQueue(user.getId());
        userDeletionQueueRepository.save(userDeletionQueue);

        //And finally
        userRepository.delete(user);

        //update counters
        updateUserCounter();

        log.info("Deleted user " + user.getUsername());
    }

    private void updateUserCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        final Long totalUsers = userRepository.count();

        // Unlikely for this to happen, but you never know...
        if (totalUsersCounter == null) {
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
        }

        totalUsersCounter.setValue(totalUsers);
        counterRepository.save(totalUsersCounter);
    }

    public void sendPasswordResetRequest(@Valid String email) {
        final var user = userRepository.findByUsername(email)
                .orElseThrow(() -> new UserNotFoundByUsernameException(email));

        final var resetToken = jwtTokenUtil.generateToken(user);
        sendPasswordResetEmail(user, resetToken);

        log.info("Password reset email sent to user: " + user.getUsername());
    }

    /**
     * Sends a password reset email to the user with the JWT token.
     *
     * @param user The user to send the email to
     * @param resetToken The JWT token for password reset
     */
    private void sendPasswordResetEmail(User user, String resetToken) {
        final var language = translationService.getCurrentLanguage();
        final var subject = getLocalizedSubject(language.getCode());
        final var emailBody = createPasswordResetEmailBody(language, user, resetToken);

        emailService.sendSimpleMessage(
                Optional.of("Visited App"),
                Optional.empty(), // Use default sender
                user.getUsername(),
                subject,
                emailBody,
                true // Add unsubscribe header
        );
    }

    public String getLocalizedSubject(String languageCode) {
        return switch (languageCode) {
            case "ca" -> "Visited App Restabliment de Contrasenya";
            case "cs" -> "Visited App Obnovení Hesla";
            case "da" -> "Visited App Nulstilling af Adgangskode";
            case "de" -> "Visited App Passwort zurücksetzen";
            case "el" -> "Visited App Επαναφορά Κωδικού";
            case "en" -> "Visited App Password Reset";
            case "es" -> "Visited App Restablecimiento de Contraseña";
            case "fi" -> "Visited App Salasanan Nollaus";
            case "fr" -> "Visited App Réinitialisation du Mot de Passe";
            case "hr" -> "Visited App Reset Lozinke";
            case "hu" -> "Visited App Jelszó Visszaállítás";
            case "id" -> "Visited App Reset Kata Sandi";
            case "it" -> "Visited App Reimposta Password";
            case "ja" -> "Visited App パスワードリセット";
            case "ko" -> "Visited App 비밀번호 재설정";
            case "ms" -> "Visited App Tetapan Semula Kata Laluan";
            case "nb" -> "Visited App Tilbakestill Passord";
            case "nl" -> "Visited App Wachtwoord Resetten";
            case "pl" -> "Visited App Reset Hasła";
            case "pt" -> "Visited App Redefinição de Senha";
            case "ro" -> "Visited App Resetare Parolă";
            case "ru" -> "Visited App Сброс Пароля";
            case "sk" -> "Visited App Obnovenie Hesla";
            case "sr" -> "Visited App Ресет Лозинке";
            case "sv" -> "Visited App Återställ Lösenord";
            case "th" -> "Visited App รีเซ็ตรหัสผ่าน";
            case "tr" -> "Visited App Şifre Sıfırlama";
            case "uk" -> "Visited App Скидання Пароля";
            case "vi" -> "Visited App Đặt Lại Mật Khẩu";
            case "zh-Hans" -> "Visited App 重置密码";
            case "zh-Hant" -> "Visited App 重設密碼";
            default -> "Visited App Password Reset"; // fallback
        };
    }

    /**
     * Creates the HTML email body for password reset.
     *
     * @param user The user requesting password reset
     * @param resetToken The JWT token for password reset
     * @return HTML email body
     */
    private String createPasswordResetEmailBody(SupportedLanguage language, User user, String resetToken) {
        String resetUrl = config.getHost() +  "/v2/auth/resetPassword?token=" + resetToken + "&lang=" + language.getCode();

        final var fileName = "templates/resetPasswordEmail/" + language.getCode() + ".html";
        return resourceReader.readResource(fileName).formatted(resetUrl);
    }

    /**
     * Resets a user's password using a valid reset token.
     *
     * @param token The password reset token
     * @param newPassword The new password
     * @throws RuntimeException if the token is invalid or user not found
     */

    public TokenResponse resetPassword(String token, String newPassword, Platform platform) {
        final var userName = jwtTokenUtil.getUsernameFromToken(token);

        final var user = userRepository.findByUsername(userName)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Encode and set the new password
        String encodedPassword = passwordEncoder.encode(newPassword);
        user.setPassword(encodedPassword);
        user.setLastPasswordResetDate(new Date());

        userRepository.save(user);

        log.info("Password successfully reset for user: " + user.getUsername());

        final var result = sessionService.startNewSession(
                new AuthRequest(user.getUsername(), newPassword, platform));
        return new TokenResponse(result.token(), userBuilder.createDTOFromUser((User) result.userDetails()));
    }
}
