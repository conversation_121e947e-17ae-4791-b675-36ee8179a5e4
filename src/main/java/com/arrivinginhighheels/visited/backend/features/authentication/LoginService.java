package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.features.iap.UserPurchase;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import org.springframework.stereotype.Service;

/**
 * Service responsible for the login behavior of the system
 */
@Service
public class LoginService {


    private final UserBuilder userBuilder;
    private final SelectionService selectionService;
    private final UserPurchasesRepository userPurchasesRepository;
    private final UserRepository userRepository;
    private final SessionService sessionService;

    public LoginService(
            UserBuilder userBuilder,
            SelectionService selectionService,
            UserPurchasesRepository userPurchasesRepository,
            UserRepository userRepository,
            SessionService sessionService) {
        this.userBuilder = userBuilder;
        this.selectionService = selectionService;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userRepository = userRepository;
        this.sessionService = sessionService;
    }

    @Deprecated
    public TokenResponseWithStats oldLogin(
            final AuthRequest authRequest,
            final boolean fetchSelections
    ) {
        final var result = sessionService.startNewSession(authRequest);

        final var purchases = userPurchasesRepository
                .findAllByUser((User) result.userDetails())
                .stream()
                .map(UserPurchase::getProductId)
                .toList();

        final var user = userBuilder.createDTOFromUser((User) result.userDetails());

        if (!fetchSelections) {
            return new TokenResponseWithStats(
                    result.token(),
                    user,
                    null,
                    purchases
            );
        }

        final var selections = selectionService.getUserSelections((User) result.userDetails());
        return new TokenResponseWithStats(
                result.token(),
                user,
                selections,
                purchases
        );
    }

    public TokenResponse login(AuthRequest request) {
        final var result = sessionService.startNewSession(request);
        final var user = userBuilder.createDTOFromUser((User) result.userDetails());
        return new TokenResponse(result.token(), user);
    }


    public void toggleUnsubscribed(User user) {
        user.setUnsubscribed(!user.getUnsubscribed());
        userRepository.save(user);
    }
}
