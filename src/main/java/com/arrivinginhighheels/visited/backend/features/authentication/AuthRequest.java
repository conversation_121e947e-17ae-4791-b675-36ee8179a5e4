package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.model.Platform;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.util.Objects;

/**
 * Represents the information for the login request
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AuthRequest {

    @NotNull
    @Email
    private String email;

    @Nullable
    private String password;

    @NotNull
    private Platform platform;

    @Override
    public String toString() {
        return "AuthRequest{" +
                "email='" + email + '\'' +
                ", platform=" + platform +
                '}';
    }

    @Override
    public final boolean equals(Object o) {
        if (!(o instanceof AuthRequest that)) return false;

        return Objects.equals(getEmail(), that.getEmail()) && Objects.equals(getPassword(), that.getPassword()) && Objects.equals(getPlatform(), that.getPlatform());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getEmail());
        result = 31 * result + Objects.hashCode(getPassword());
        result = 31 * result + Objects.hashCode(getPlatform());
        return result;
    }
}
