package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.dto.*;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.UserService;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNUP_URL;

/**
 * REST Controller responsible for the creation of new users.
 */
@RestController
public class SignUpRestController {

    private final UserService userService;

    private final UserBuilder userBuilder;

    private final LoginService loginService;

    public SignUpRestController(
            UserService userService,
            UserBuilder userBuilder,
            LoginService loginService
    ) {
        this.userService = userService;
        this.userBuilder = userBuilder;
        this.loginService = loginService;
    }

    /**
     * Public method for creating a new user with the minimum authority (USER).
     *
     * @param newUserInfo DTO for the minimum data necessary for the new user
     *                    creation
     * @return the newly created user information
     */
    @RequestMapping(value = SIGNUP_URL, method = RequestMethod.POST)
    @ResponseStatus(HttpStatus.CREATED)
    public TokenResponseWithStats createNewUser(@Valid @RequestBody final AuthRequest newUserInfo) {
        final User newCreatedUser = userService.createUser(newUserInfo);
        final UserDTO newUserDTO = userBuilder.createDTOFromUser(newCreatedUser);

        return loginService.oldLogin(
                new AuthRequest(
                        newUserDTO.getEmail(),
                        null,
                        newUserInfo.getPlatform()),
                false);
    }
}
