package com.arrivinginhighheels.visited.backend.features.authentication;


import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.SessionRepository;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import com.arrivinginhighheels.visited.backend.security.JwtTokenUtil;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class SessionService {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenUtil jwtTokenUtil;
    private final UserDetailsService userDetailsService;
    private final SessionRepository sessionRepository;
    private final UserRepository userRepository;

    public SessionService(AuthenticationManager authenticationManager, JwtTokenUtil jwtTokenUtil, UserDetailsService userDetailsService, SessionRepository sessionRepository, UserRepository userRepository) {
        this.authenticationManager = authenticationManager;
        this.jwtTokenUtil = jwtTokenUtil;
        this.userDetailsService = userDetailsService;
        this.sessionRepository = sessionRepository;
        this.userRepository = userRepository;
    }

    public TokenAndUserDetails startNewSession(AuthRequest authRequest) {
        // Perform the security
        final var caseInsensitiveEmail = authRequest.getEmail().toLowerCase();

        var accountDoesNotExist = emailAvailable(caseInsensitiveEmail);
        if (accountDoesNotExist) {
            throw new UserNotFoundByUsernameException(caseInsensitiveEmail);

        }

        var password = authRequest.getPassword();
        if (password == null) {
            password = caseInsensitiveEmail;
        }

        final var authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(caseInsensitiveEmail, password)
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);

        // Reload password post-security so we can generate token
        final var userDetails = userDetailsService.loadUserByUsername(caseInsensitiveEmail);

        final var token = jwtTokenUtil.generateToken(userDetails);

        // Saves session information
        final Session session = new Session((User) userDetails, token, authRequest.getPlatform(), LocalDateTime.now());
        sessionRepository.save(session);
        return new TokenAndUserDetails(userDetails, token);
    }

    public boolean emailAvailable(String email) {
        return userRepository.findByUsername(email).isEmpty();
    }
}

