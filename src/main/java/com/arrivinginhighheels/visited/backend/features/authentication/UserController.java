package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.UserService;
import com.arrivinginhighheels.visited.backend.utils.ResourceReader;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AUTHENTICATION_URL;

@RestController
@RequestMapping(path = AUTHENTICATION_URL)
public class UserController {

    private final LoginService loginService;

    private final UserService userService;

    private final UserBuilder userBuilder;

    private final LoggedInUserUtil loggedInUserUtil;

    private final ResourceReader resourceReader;

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    public UserController(
            LoginService loginService,
            UserService userService,
            UserBuilder userBuilder,
            LoggedInUserUtil loggedInUserUtil,
            ResourceReader resourceReader) {
        this.loginService = loginService;
        this.userService = userService;
        this.userBuilder = userBuilder;
        this.loggedInUserUtil = loggedInUserUtil;
        this.resourceReader = resourceReader;
    }

    @PostMapping(path = "signup")
    public ResponseEntity<TokenResponse> signup(
            @RequestBody @Valid AuthRequest request) {
        userService.createUser(request);
        final var token = loginService.login(request);

        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(token);
    }

    @PostMapping(path = "login")
    public TokenResponse login(
            @RequestBody @Valid AuthRequest request) {
        final var tokenResponse = loginService.login(request);
        log.info("Login request successful for the user " + tokenResponse.user().getEmail());
        return tokenResponse;
    }

    @PostMapping(path = "toggleUnsubscribed")
    public UserDTO toggleUnsubscribed(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        loginService.toggleUnsubscribed(user);
        return userBuilder.createDTOFromUser(user);
    }

    @PostMapping(path = "requestPasswordReset")
    public ResponseEntity<Boolean> requestPasswordResetToken(
            @RequestBody @Valid ResetPasswordRequest request) {
        userService.sendPasswordResetRequest(request.email());
        return ResponseEntity.ok(true);
    }

    @GetMapping(path = "resetPassword", produces = "text/html")
    public ResponseEntity<String> confirmPasswordResetToken(
            @RequestParam String token,
            @RequestParam String languageCode,
            HttpServletRequest request
    ) {
        // Determine the appropriate app store URL based on user agent
        String appStoreUrl = determineAppStoreUrl(request);

        // Load the HTML template and inject the app store URL
        String templatePath = "templates/resetPassword/" + languageCode + ".html";
        String htmlContent;

        try {
            htmlContent = resourceReader.readResource(templatePath);
            // Format the template with the app store URL
            htmlContent = htmlContent.formatted(appStoreUrl);
        } catch (Exception e) {
            // Fallback to English template if the requested language is not available
            log.warn("Template not found for language: " + languageCode + ", falling back to English");
            htmlContent = resourceReader.readResource("templates/resetPassword/en.html");
            htmlContent = htmlContent.formatted(appStoreUrl);
        }

        return ResponseEntity.ok(htmlContent);
    }

    /**
     * Determines the appropriate app store URL based on the user agent
     */
    private String determineAppStoreUrl(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");

        if (userAgent != null && userAgent.toLowerCase().contains("android")) {
            // Android device - redirect to Google Play Store
            return "https://play.google.com/store/apps/details?id=com.arrivinginhighheels.visited";
        } else {
            // Default to iOS App Store (covers iOS devices and general fallback)
            return "https://apps.apple.com/app/id846983349";
        }
    }

    @PostMapping(path = "confirmPasswordReset")
    public TokenResponse confirmPasswordResetToken(
            @RequestBody @Valid ConfirmPasswordResetRequest request) {
        return userService.resetPassword(request.token(), request.newPassword(), request.platform());
    }
}
