package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.UserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AUTHENTICATION_URL;

@RestController
@RequestMapping(path = AUTHENTICATION_URL)
public class UserController {

    private final LoginService loginService;

    private final UserService userService;

    private final UserBuilder userBuilder;

    private final LoggedInUserUtil loggedInUserUtil;

    private static final Logger log = LoggerFactory.getLogger(UserController.class);

    public UserController(
            LoginService loginService,
            UserService userService,
            UserBuilder userBuilder,
            LoggedInUserUtil loggedInUserUtil) {
        this.loginService = loginService;
        this.userService = userService;
        this.userBuilder = userBuilder;
        this.loggedInUserUtil = loggedInUserUtil;
    }

    @PostMapping(path = "signup")
    public ResponseEntity<TokenResponse> signup(
            @RequestBody @Valid AuthRequest request) {
        userService.createUser(request);
        final var token = loginService.login(request);

        return ResponseEntity
                .status(HttpStatus.CREATED)
                .body(token);
    }

    @PostMapping(path = "login")
    public TokenResponse login(
            @RequestBody @Valid AuthRequest request) {
        final var tokenResponse = loginService.login(request);
        log.info("Login request successful for the user " + tokenResponse.user().getEmail());
        return tokenResponse;
    }

    @PostMapping(path = "toggleUnsubscribed")
    public UserDTO toggleUnsubscribed(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        loginService.toggleUnsubscribed(user);
        return userBuilder.createDTOFromUser(user);
    }

    @PostMapping(path = "requestPasswordReset")
    public ResponseEntity<Boolean> requestPasswordResetToken(
            @RequestBody @Valid ResetPasswordRequest request) {
        userService.sendPasswordResetRequest(request.email());
        return ResponseEntity.ok(true);
    }

    @GetMapping(path = "resetPassword", produces = "text/html")
    public ResponseEntity<String> confirmPasswordResetToken(
            @RequestParam String token,
            @RequestParam String languageCode
    ) {
        return ResponseEntity.ok(true);
    }

    @PostMapping(path = "confirmPasswordReset")
    public TokenResponse confirmPasswordResetToken(
            @RequestBody @Valid ConfirmPasswordResetRequest request) {
        return userService.resetPassword(request.token(), request.newPassword(), request.platform());
    }
}
