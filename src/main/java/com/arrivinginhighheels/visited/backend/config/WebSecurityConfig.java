package com.arrivinginhighheels.visited.backend.config;

import com.arrivinginhighheels.visited.backend.security.JwtAuthenticationEntryPoint;
import com.arrivinginhighheels.visited.backend.security.JwtAuthenticationTokenFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.TRACKED_LINK_URL;

/**
 * Bean to configure Spring Security logic.
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    private final JwtAuthenticationEntryPoint unauthorizedHandler;
    private final UserDetailsService userDetailsService;

    public WebSecurityConfig(
            JwtAuthenticationEntryPoint unauthorizedHandler,
            UserDetailsService userDetailsService
    ) {
        this.unauthorizedHandler = unauthorizedHandler;
        this.userDetailsService = userDetailsService;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable) // Disable CSRF for stateless APIs
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(handling -> handling.authenticationEntryPoint(unauthorizedHandler))
            .authorizeHttpRequests(auth -> auth
                // CORS pre-flight usually needs this. Since there's no other use for OPTIONS in
                // this app, it is
                // always permitted.
                .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()

                // allow public access to health-check method for the Load-Balancer
                .requestMatchers(HttpMethod.GET, "/hc").permitAll()

                // login entry point. Public.
                .requestMatchers(HttpMethod.POST, "/v2/signin").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/signinformigration").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/signup").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/signup/available").permitAll()

                // this is the users' sign-up entry-point. Since it's a public method, it is
                // always permitted.
                .requestMatchers(HttpMethod.POST, "/v2/signupformigration").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/auth/login").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/auth/signup").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/auth/requestPasswordReset").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/auth/confirmPasswordReset").permitAll()

                .requestMatchers(HttpMethod.GET, "/v2/areas").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/areas/updated").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/areas/{id}/subdivisions").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/areas/{id}/topPlaces").fullyAuthenticated()
                .requestMatchers(HttpMethod.GET, "/v2/areas/{id}/details").fullyAuthenticated()
                .requestMatchers(HttpMethod.GET, "/v2/areas/{id}/notes").fullyAuthenticated()
                .requestMatchers(HttpMethod.POST, "/v2/areas/{id}/notes").fullyAuthenticated()
                .requestMatchers(HttpMethod.GET, "/v2/areas/disputed").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/areas/{id}/cities").permitAll()

                .requestMatchers(HttpMethod.GET, "/v2/geometry").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/geometry/whitelist").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/geometry/{id}").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/geometry/{id}/subdivisions").permitAll()

                .requestMatchers(HttpMethod.GET, "/v2/cities/id/{id}").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/cities/areas/{id}").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/cities/search").permitAll()

                .requestMatchers(HttpMethod.GET, "/v2/lists").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/lists/{id}").permitAll()

                .requestMatchers(HttpMethod.POST, "/v2/stripe/webhook").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/stripe/webhook").permitAll()
                .requestMatchers(HttpMethod.PUT, "/v2/stripe/webhook").permitAll()
                .requestMatchers(HttpMethod.DELETE, "/v2/stripe/webhook").permitAll()

                .requestMatchers(HttpMethod.GET, TRACKED_LINK_URL).permitAll()
                .requestMatchers(HttpMethod.GET, TRACKED_LINK_URL + "/**").permitAll()

                .requestMatchers(HttpMethod.POST, "/v2/receiptValidation/notification").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/receiptValidation/google_webhook").permitAll()
                .requestMatchers(HttpMethod.GET, "/v2/email/unsubscribe").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/email/maileroo_webhook").permitAll()
                .requestMatchers(HttpMethod.POST, "/v2/sns").permitAll()
                .requestMatchers(HttpMethod.GET, "/admin/**").permitAll()

                .requestMatchers(HttpMethod.GET, "/apple-app-site-association").permitAll()
                .requestMatchers(HttpMethod.GET, "/.well-known/assetlinks.json").permitAll()
//                .requestMatchers(HttpMethod.GET, "/**/*.png", "/**/*.json").permitAll()

                // Public endpoints
                .anyRequest().authenticated() // Secure everything else
                )
            .addFilterBefore(authenticationTokenFilterBean(), UsernamePasswordAuthenticationFilter.class)
            .headers(header -> header.cacheControl(HeadersConfigurer.CacheControlConfig::disable))
            .httpBasic(basic -> {}); // Enable HTTP Basic Authentication
        return http.build();
    }

    /**
     * Configures the Auth Manager Builder.
     *
     * @param authenticationManagerBuilder injected.
     * @throws Exception
     */
    @Autowired
    public void configureAuthentication(final AuthenticationManagerBuilder authenticationManagerBuilder)
            throws Exception {
        authenticationManagerBuilder
                .userDetailsService(userDetailsService)
                .passwordEncoder(new BCryptPasswordEncoder());
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration)
            throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public JwtAuthenticationTokenFilter authenticationTokenFilterBean() {
        return new JwtAuthenticationTokenFilter();
    }

}
