<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited App - 重置密码</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .logo {
            margin-bottom: 30px;
            font-size: 32pt;
            color: #092a68;
            font-weight: bold;
        }
        .logo img {
            height: 60px;
            width: auto;
        }
        h1 {
            color: #092a68;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .message {
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #555;
        }
        .app-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .app-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #092a68;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            min-width: 140px;
        }
        .app-button:hover {
            background-color: #0a3278;
            color: white;
            text-decoration: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #092a68;
        }
        .instructions h3 {
            margin-top: 0;
            color: #092a68;
        }
        .step {
            margin: 10px 0;
            text-align: left;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .app-buttons {
                flex-direction: column;
                align-items: center;
            }
            .app-button {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img alt="Visited_logo" align="center" src="/images/visited_logo.png" />
        <div class="logo">Visited</div>

        <h1>哎呀！快完成了...</h1>

        <div class="message">
            <p>看起来您是从网页浏览器点击了密码重置链接。要重置密码，您需要使用Visited手机应用程序。</p>

            <p><strong>请按照以下步骤操作：</strong></p>
        </div>

        <div class="instructions">
            <h3>如何重置密码：</h3>
            <div class="step">1. 在您的手机或平板电脑上下载Visited应用程序</div>
            <div class="step">2. 在您的移动设备上打开密码重置邮件</div>
            <div class="step">3. 从您的移动设备点击密码重置链接</div>
            <div class="step">4. 应用程序将打开并指导您完成密码重置</div>
        </div>

        <div class="app-buttons">
            <a href="%s" class="app-button" id="download-button">下载Visited应用</a>
        </div>

        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
            在 <a href="https://visitedapp.com/" style="color: #092a68;">visitedapp.com</a> 了解更多关于Visited的信息<br>
            需要帮助？请联系我们：<a href="mailto:<EMAIL>" style="color: #092a68;"><EMAIL></a>
        </p>
    </div>
</body>
</html>
