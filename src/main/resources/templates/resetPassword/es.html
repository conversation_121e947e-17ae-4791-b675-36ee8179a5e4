<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited App - Restablecer <PERSON>eña</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .logo {
            font-size: 2.5em;
            color: #092a68;
            margin-bottom: 20px;
            font-weight: bold;
        }
        h1 {
            color: #092a68;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .message {
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #555;
        }
        .app-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .app-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #092a68;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            min-width: 140px;
        }
        .app-button:hover {
            background-color: #0a3278;
            color: white;
            text-decoration: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #092a68;
        }
        .instructions h3 {
            margin-top: 0;
            color: #092a68;
        }
        .step {
            margin: 10px 0;
            text-align: left;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .app-buttons {
                flex-direction: column;
                align-items: center;
            }
            .app-button {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📍 Visited</div>
        
        <h1>¡Ups! Casi llegamos...</h1>
        
        <div class="message">
            <p>Parece que hiciste clic en el enlace de restablecimiento de contraseña desde un navegador web. Para restablecer tu contraseña, necesitarás usar la aplicación móvil de Visited.</p>
            
            <p><strong>Por favor, sigue estos pasos:</strong></p>
        </div>
        
        <div class="instructions">
            <h3>Cómo restablecer tu contraseña:</h3>
            <div class="step">1. Descarga la aplicación Visited en tu teléfono o tablet</div>
            <div class="step">2. Abre el correo de restablecimiento de contraseña en tu dispositivo móvil</div>
            <div class="step">3. Toca el enlace de restablecimiento de contraseña desde tu dispositivo móvil</div>
            <div class="step">4. La aplicación se abrirá y te guiará para restablecer tu contraseña</div>
        </div>
        
        <div class="app-buttons">
            <a href="%s" class="app-button" id="download-button">Descargar Visited App</a>
        </div>
        
        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
            ¿Necesitas ayuda? Contáctanos en <a href="mailto:<EMAIL>" style="color: #092a68;"><EMAIL></a>
        </p>
    </div>
</body>
</html>
