<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited App - 重設密碼</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .logo {
            margin-bottom: 30px;
            font-size: 32pt;
            color: #092a68;
            font-weight: bold;
        }
        .logo img {
            height: 60px;
            width: auto;
        }
        h1 {
            color: #092a68;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .message {
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #555;
        }
        .app-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .app-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #092a68;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            min-width: 140px;
        }
        .app-button:hover {
            background-color: #0a3278;
            color: white;
            text-decoration: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #092a68;
        }
        .instructions h3 {
            margin-top: 0;
            color: #092a68;
        }
        .step {
            margin: 10px 0;
            text-align: left;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .app-buttons {
                flex-direction: column;
                align-items: center;
            }
            .app-button {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img alt="Visited_logo" align="center" src="/images/visited_logo.png" />
        <div class="logo">Visited</div>
        
        <h1>哎呀！快完成了...</h1>
        
        <div class="message">
            <p>看起來您是從網頁瀏覽器點擊了密碼重設連結。要重設密碼，您需要使用Visited手機應用程式。</p>
            
            <p><strong>請按照以下步驟操作：</strong></p>
        </div>
        
        <div class="instructions">
            <h3>如何重設密碼：</h3>
            <div class="step">1. 在您的手機或平板電腦上下載Visited應用程式</div>
            <div class="step">2. 在您的行動裝置上開啟密碼重設郵件</div>
            <div class="step">3. 從您的行動裝置點擊密碼重設連結</div>
            <div class="step">4. 應用程式將開啟並指導您完成密碼重設</div>
        </div>
        
        <div class="app-buttons">
            <a href="%s" class="app-button" id="download-button">下載Visited應用程式</a>
        </div>
        
        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
            在 <a href="https://visitedapp.com/" style="color: #092a68;">visitedapp.com</a> 了解更多關於Visited的資訊<br>
            需要協助？請聯絡我們：<a href="mailto:<EMAIL>" style="color: #092a68;"><EMAIL></a>
        </p>
    </div>
</body>
</html>
