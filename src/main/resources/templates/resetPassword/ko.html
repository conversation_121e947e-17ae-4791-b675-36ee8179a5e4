<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited App - 비밀번호 재설정</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .logo {
            margin-bottom: 30px;
            font-size: 32pt;
            color: #092a68;
            font-weight: bold;
        }
        .logo img {
            height: 60px;
            width: auto;
        }
        h1 {
            color: #092a68;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .message {
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #555;
        }
        .app-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .app-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #092a68;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            min-width: 140px;
        }
        .app-button:hover {
            background-color: #0a3278;
            color: white;
            text-decoration: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #092a68;
        }
        .instructions h3 {
            margin-top: 0;
            color: #092a68;
        }
        .step {
            margin: 10px 0;
            text-align: left;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .app-buttons {
                flex-direction: column;
                align-items: center;
            }
            .app-button {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img alt="Visited_logo" align="center" src="/images/visited_logo.png" />
        <div class="logo">Visited</div>
        
        <h1>앗! 거의 다 왔어요...</h1>
        
        <div class="message">
            <p>웹 브라우저에서 비밀번호 재설정 링크를 클릭하신 것 같습니다. 비밀번호를 재설정하려면 Visited 모바일 앱을 사용해야 합니다.</p>
            
            <p><strong>다음 단계를 따라주세요:</strong></p>
        </div>
        
        <div class="instructions">
            <h3>비밀번호 재설정 방법:</h3>
            <div class="step">1. 휴대폰이나 태블릿에 Visited 앱을 다운로드하세요</div>
            <div class="step">2. 모바일 기기에서 비밀번호 재설정 이메일을 여세요</div>
            <div class="step">3. 모바일 기기에서 비밀번호 재설정 링크를 탭하세요</div>
            <div class="step">4. 앱이 열리고 비밀번호 재설정 과정을 안내해드립니다</div>
        </div>
        
        <div class="app-buttons">
            <a href="%s" class="app-button" id="download-button">Visited 앱 다운로드</a>
        </div>
        
        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
            Visited에 대해 더 알아보기: <a href="https://visitedapp.com/" style="color: #092a68;">visitedapp.com</a><br>
            도움이 필요하신가요? 문의하기: <a href="mailto:<EMAIL>" style="color: #092a68;"><EMAIL></a>
        </p>
    </div>
</body>
</html>
