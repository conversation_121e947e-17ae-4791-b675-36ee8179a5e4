<!DOCTYPE html>
<html lang="hr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited A<PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .logo {
            margin-bottom: 30px;
            font-size: 32pt;
            color: #092a68;
            font-weight: bold;
        }
        .logo img {
            height: 60px;
            width: auto;
        }
        h1 {
            color: #092a68;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .message {
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #555;
        }
        .app-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }
        .app-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #092a68;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            min-width: 140px;
        }
        .app-button:hover {
            background-color: #0a3278;
            color: white;
            text-decoration: none;
        }
        .instructions {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #092a68;
        }
        .instructions h3 {
            margin-top: 0;
            color: #092a68;
        }
        .step {
            margin: 10px 0;
            text-align: left;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            .app-buttons {
                flex-direction: column;
                align-items: center;
            }
            .app-button {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <img alt="Visited_logo" align="center" src="/images/visited_logo.png" />
        <div class="logo">Visited</div>
        
        <h1>Ups! Skoro smo tu...</h1>
        
        <div class="message">
            <p>Čini se da ste kliknuli na vezu za resetiranje lozinke iz web preglednika. Za resetiranje lozinke trebat ćete koristiti Visited mobilnu aplikaciju.</p>
            
            <p><strong>Molimo slijedite ove korake:</strong></p>
        </div>
        
        <div class="instructions">
            <h3>Kako resetirati lozinku:</h3>
            <div class="step">1. Preuzmite Visited aplikaciju na svoj telefon ili tablet</div>
            <div class="step">2. Otvorite email za resetiranje lozinke na svom mobilnom uređaju</div>
            <div class="step">3. Dodirnite vezu za resetiranje lozinke sa svog mobilnog uređaja</div>
            <div class="step">4. Aplikacija će se otvoriti i voditi vas kroz resetiranje lozinke</div>
        </div>
        
        <div class="app-buttons">
            <a href="%s" class="app-button" id="download-button">Preuzmite Visited App</a>
        </div>
        
        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
            Saznajte više o Visited na <a href="https://visitedapp.com/" style="color: #092a68;">visitedapp.com</a><br>
            Trebate pomoć? Kontaktirajte nas na <a href="mailto:<EMAIL>" style="color: #092a68;"><EMAIL></a>
        </p>
    </div>
</body>
</html>
