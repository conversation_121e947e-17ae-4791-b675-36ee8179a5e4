package com.arrivinginhighheels.visited.backend.selection;

import com.arrivinginhighheels.visited.backend.dto.SelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SELECT_URL;
import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNIN_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.*;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the select method(s) and its(their) behaviour
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SelectCountryTests {

    public static final String NEWUSER_EMAIL = "<EMAIL>";

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testSelectionOfACountryWithLived() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(userSelections.getLivedAreas(), is(notNullValue()));
        assertThat(userSelections.getLivedAreas().size(), is(1)); //only the country
        assertThat(userSelections.getLivedAreas(), hasItem(BRAZIL_ISO_KEY));
    }

    @Test
    public void testThatOnlyOneCountryThatHasNoSubdivisionCanBeSelectedAsLived() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, SelectionType.LIVED);
        SelectionsDTO selections = selectAreaForTheLoggedInUser(securedAPI,  ITALY_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(selections.getLivedAreas().size(), is(1)); //only one country as lived can be selected
        assertThat(selections.getLivedAreas(), hasItem(ITALY_ISO_KEY));

        //and: checks that Brazil is CLEARED (do not show in any of the areas lists)
        assertThat(selections.getLivedAreas(), not(hasItem(BRAZIL_ISO_KEY)));
        assertThat(selections.getBeenAreas(), not(hasItem(BRAZIL_ISO_KEY)));
        assertThat(selections.getWantAreas(), not(hasItem(BRAZIL_ISO_KEY)));
    }


    @Test
    public void testSelectionOfACountryWithBeen() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), ITALY_ISO_KEY, SelectionType.BEEN);

        //then:
        assertThat(userSelections.getBeenAreas(), is(notNullValue()));
        assertThat(userSelections.getBeenAreas().size(), is(1)); //only one country should appear
        assertThat(userSelections.getBeenAreas(), hasItem(ITALY_ISO_KEY));
    }

    @Test
    public void testSelectionOfACountryWithWant() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), MADAGASCAR_ISO_KEY, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), is(notNullValue()));
        assertThat(userSelections.getWantAreas(), hasItem(MADAGASCAR_ISO_KEY));
    }

    @Test
    public void testVisualizationOfTravelStatsInSignUpAndSigninResponse() {
        //given: new user created
        createNewUser(this.restTemplate, NEWUSER_EMAIL);

        //when: select
        selectAreaForTheUser(securedAPI, NEWUSER_EMAIL, BRAZIL_ISO_KEY, SelectionType.LIVED);
        selectAreaForTheLoggedInUser(securedAPI,  ITALY_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI,  GERMANY_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI,  IRELAND_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI,  MADAGASCAR_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI,  PORTUGAL_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI,  SPAIN_ISO_KEY, SelectionType.WANT);

        ///and signin with the new user again
        var tokenResponse = this.restTemplate.postForObject(SIGNIN_URL,
                                new AuthRequest(NEWUSER_EMAIL, null, IOS_PLATFORM), TokenResponseWithStats.class);

        //then: the selections collections should reflect the new values
        assertThat(tokenResponse.token(), is(notNullValue()));
        assertThat(tokenResponse.selections(), is(notNullValue()));
        assertThat(tokenResponse.selections().getLivedAreas(), hasItem(BRAZIL_ISO_KEY));
        assertThat(tokenResponse.selections().getLivedAreas().size(), is(1));

        assertThat(tokenResponse.selections().getBeenAreas(), hasItem(ITALY_ISO_KEY));
        assertThat(tokenResponse.selections().getBeenAreas(), hasItem(GERMANY_ISO_KEY));
        assertThat(tokenResponse.selections().getBeenAreas().size(), is(2));

        assertThat(tokenResponse.selections().getWantAreas(), hasItem(IRELAND_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas(), hasItem(MADAGASCAR_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas(), hasItem(PORTUGAL_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas(), hasItem(SPAIN_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas().size(), is(4));
    }

    @Test
    public void testDeSelectionOfACountry() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), MADAGASCAR_ISO_KEY, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), hasItem(MADAGASCAR_ISO_KEY));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI,  MADAGASCAR_ISO_KEY, SelectionType.CLEAR);

        //then:
        assertThat(userSelections.getLivedAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getWantAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
    }

    @Test
    public void testChangeSelectionsTypeOfACountry() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), MADAGASCAR_ISO_KEY, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), hasItem(MADAGASCAR_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI,  MADAGASCAR_ISO_KEY, SelectionType.BEEN);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), hasItem(MADAGASCAR_ISO_KEY));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI,  MADAGASCAR_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), hasItem(MADAGASCAR_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI,  MADAGASCAR_ISO_KEY, SelectionType.CLEAR);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(MADAGASCAR_ISO_KEY)));
    }

    @Test
    public void testThatASelectionOfANonExistantGeoAreaShould404() {
        SelectionDTO selection = new SelectionDTO("BRZFRG", SelectionType.BEEN);
        ResponseEntity<SelectionsDTO> resp =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .post(SELECT_URL, selection, SelectionsDTO.class);

        assertThat(resp.getStatusCode(), is(HttpStatus.NOT_FOUND));
    }

}
