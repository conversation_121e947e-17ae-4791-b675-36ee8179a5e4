package com.arrivinginhighheels.visited.backend.privacy;

import com.arrivinginhighheels.visited.backend.dto.PrivacyAgreementDTO;
import com.arrivinginhighheels.visited.backend.dto.PrivacyDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.features.privacy.TriStateValue;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import com.arrivinginhighheels.visited.backend.utils.SelectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.CASL_URL;
import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNUP_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.CANADA_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.CANADA_ON_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.USA_KS_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.CANADIAN_USER;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests related to the new CASL requirement for the app
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PrivacyTests {

    public static final String NON_CANADIAN_USER_EMAIL = "<EMAIL>";

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testCASLReturnForANewCanadianUser() {
        //given:
        var userDTO = new AuthRequest(CANADIAN_USER.getEmail(), null, IOS_PLATFORM);

        //when signup the new user:
        var resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);

        //then:
        assertThat(resp.getStatusCode(), is(HttpStatus.CREATED));

        //when: I use the recently created user (still without a lived region) and get the privacy status
        ResponseEntity<PrivacyDTO> respCASL =
                securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                          .get(CASL_URL, PrivacyDTO.class);
        PrivacyDTO casl = respCASL.getBody();

        //then: should not have casl required, as the user did not selected any lived area
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
//        assertThat(casl.isRequired(), is(Boolean.FALSE));
        assertThat(casl.getOptIn(), is(TriStateValue.NA));
        assertThat(casl.getTerms(), is(TriStateValue.NA));
        assertThat(casl.getTimestamp(), is(nullValue()));

        //when: the user select a canadian area as "LIVED"
        SelectionsDTO selectionsDTO = SelectionUtils.selectAreaForTheUser(securedAPI, CANADIAN_USER.getEmail(),
                CANADA_ON_ISO_KEY, SelectionType.LIVED);

        //then: assert the return is OK
        assertThat(selectionsDTO.getLivedAreas(), hasItem(CANADA_ON_ISO_KEY));

        //when: check again the casl method
        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                             .get(CASL_URL, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then: now should have casl required, as the user did select a canadian area as "LIVED"
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.TRUE));
        assertThat(casl.getOptIn(), is(TriStateValue.NA));
        assertThat(casl.getTerms(), is(TriStateValue.NA));
        assertThat(casl.getTimestamp(), is(nullValue()));

        //when: the user select a non-canadian area as "LIVED"
        selectionsDTO = SelectionUtils.selectAreaForTheUser(securedAPI, CANADIAN_USER.getEmail(),
                USA_KS_ISO_KEY, SelectionType.LIVED);

        //then: assert the return is OK and there's no canadian areas as lived
        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(CANADA_ON_ISO_KEY)));
        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(CANADA_ISO_KEY)));

        //when: I check the casl method again
        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                .get(CASL_URL, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then: since the user had lived in Canada in the past, should still return required
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.TRUE));
        assertThat(casl.getOptIn(), is(TriStateValue.NA));
        assertThat(casl.getTerms(), is(TriStateValue.NA));
        assertThat(casl.getTimestamp(), is(nullValue()));

        // -----------------------

        //when: I optin to CASL
        PrivacyAgreementDTO privacyAgreementDTO = new PrivacyAgreementDTO();
        privacyAgreementDTO.setOptIn(TriStateValue.YES);
        privacyAgreementDTO.setTerms(TriStateValue.YES);

        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                             .post(CASL_URL, privacyAgreementDTO, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then it should return the updated casl info
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.FALSE));
        assertThat(casl.getOptIn(), is(TriStateValue.YES));
        assertThat(casl.getTerms(), is(TriStateValue.YES));
        assertThat(casl.getTimestamp(), is(notNullValue()));

        //when: I request again the CASL information for the user
        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                             .get(CASL_URL, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then: the updated CASL information should return as expected
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.FALSE));
        assertThat(casl.getOptIn(), is(TriStateValue.YES));
        assertThat(casl.getTerms(), is(TriStateValue.YES));
        assertThat(casl.getTimestamp(), is(notNullValue()));

        //when: I opt-out to CASL
        privacyAgreementDTO = new PrivacyAgreementDTO();
        privacyAgreementDTO.setOptIn(TriStateValue.NO);
        privacyAgreementDTO.setTerms(TriStateValue.NO);

        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                             .post(CASL_URL, privacyAgreementDTO, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then: the updated CASL information should return as expected
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.FALSE));
        assertThat(casl.getOptIn(), is(TriStateValue.NO));
        assertThat(casl.getTerms(), is(TriStateValue.NO));
        assertThat(casl.getTimestamp(), is(notNullValue()));

        //when: I request again the CASL information for the user
        respCASL = securedAPI.withCredentials(CANADIAN_USER.getEmail(), IOS_PLATFORM)
                .get(CASL_URL, PrivacyDTO.class);
        casl = respCASL.getBody();

        //then: the updated CASL information should return as expected
        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
        assertThat(casl.isRequired(), is(Boolean.FALSE));
        assertThat(casl.getOptIn(), is(TriStateValue.NO));
        assertThat(casl.getTerms(), is(TriStateValue.NO));
        assertThat(casl.getTimestamp(), is(notNullValue()));
    }

//    @Test
//    public void testANonCanadianNewUser() {
//        //given:
//        NewUserDTO userDTO = new NewUserDTO(NON_CANADIAN_USER_EMAIL, IOS_PLATFORM);
//
//        //when signup the new user:
//        ResponseEntity<TokenResponse> resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponse.class);
//
//        //then:
//        assertThat(resp.getStatusCode(), is(HttpStatus.CREATED));
//
//        //when: I use the recently created user to add a canadian "lived" region
//        ResponseEntity<PrivacyDTO> respCASL =
//                securedAPI.withCredentials(NON_CANADIAN_USER_EMAIL, IOS_PLATFORM)
//                        .get(CASL_URL, PrivacyDTO.class);
//        PrivacyDTO casl = respCASL.getBody();
//
//        //then: should not have casl required, as the user did not selected any lived area
//        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
//        assertThat(casl.isRequired(), is(Boolean.FALSE));
//        assertThat(casl.getOptIn(), is(TriStateValue.NA));
//        assertThat(casl.getTerms(), is(TriStateValue.NA));
//        assertThat(casl.getTimestamp(), is(nullValue()));
//
//        //when: the user select a canadian area as "LIVED"
//        SelectionsDTO selectionsDTO = SelectionUtils.selectAreaForTheUser(securedAPI, NON_CANADIAN_USER_EMAIL,
//                BRAZIL_ISO_KEY, SelectionType.LIVED);
//
//        //then: assert the return is OK
//        assertThat(selectionsDTO.getLivedAreas(), hasItem(BRAZIL_ISO_KEY));
//        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(CANADA_ISO_KEY)));
//
//        //when: check again the casl method
//        respCASL = securedAPI.withCredentials(NON_CANADIAN_USER_EMAIL, IOS_PLATFORM)
//                .get(CASL_URL, PrivacyDTO.class);
//        casl = respCASL.getBody();
//
//        //then: now should have casl required, as the user did select a canadian area as "LIVED"
//        assertThat(respCASL.getStatusCode(), is(HttpStatus.OK));
//        assertThat(casl.isRequired(), is(Boolean.FALSE));
//        assertThat(casl.getOptIn(), is(TriStateValue.NA));
//        assertThat(casl.getTerms(), is(TriStateValue.NA));
//        assertThat(casl.getTimestamp(), is(nullValue()));
//    }


//    @Test
//    public void testPrivacyReturnForANewBritishUser() {
//        //given:
//        NewUserDTO userDTO = new NewUserDTO(BRITISH_USER.getEmail(), IOS_PLATFORM);
//
//        //when signup the new user:
//        ResponseEntity<TokenResponse> resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponse.class);
//
//        //then:
//        assertThat(resp.getStatusCode(), is(HttpStatus.CREATED));
//
//        //when: I use the recently created user (still without a lived region) and get the privacy status
//        ResponseEntity<PrivacyDTO> respPrivacy =
//                securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                        .get(CASL_URL, PrivacyDTO.class);
//        PrivacyDTO privacy = respPrivacy.getBody();
//
//        //then: should not have privacy required, as the user did not selected any lived area
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.FALSE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.NA));
//        assertThat(privacy.getTerms(), is(TriStateValue.NA));
//        assertThat(privacy.getTimestamp(), is(nullValue()));
//
//        //when: the user select the england area as "LIVED"
//        SelectionsDTO selectionsDTO = SelectionUtils.selectAreaForTheUser(securedAPI, BRITISH_USER.getEmail(),
//                UK_ENGLAND_ISO_KEY, SelectionType.LIVED);
//
//        //then: assert the return is OK
//        assertThat(selectionsDTO.getLivedAreas(), hasItem(UK_ENGLAND_ISO_KEY));
//
//        //when: check again the casl method
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .get(CASL_URL, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then: now should have casl required, as the user did select a canadian area as "LIVED"
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.TRUE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.NA));
//        assertThat(privacy.getTerms(), is(TriStateValue.NA));
//        assertThat(privacy.getTimestamp(), is(nullValue()));
//
//        //when: the user select a non-UK area as "LIVED"
//        selectionsDTO = SelectionUtils.selectAreaForTheUser(securedAPI, BRITISH_USER.getEmail(),
//                USA_KS_ISO_KEY, SelectionType.LIVED);
//
//        //then: assert the return is OK and there's no UK areas as lived
//        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(UK_ENGLAND_ISO_KEY)));
//        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(UNITED_KINGDOM_ISO_KEY)));
//
//        //when: I check the privacy method again
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .get(CASL_URL, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then: since the user had lived in UK in the past, should still return required
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.TRUE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.NA));
//        assertThat(privacy.getTerms(), is(TriStateValue.NA));
//        assertThat(privacy.getTimestamp(), is(nullValue()));
//
//        // -----------------------
//
//        //when: I optin the Privacy
//        PrivacyAgreementDTO privacyAgreementDTO = new PrivacyAgreementDTO();
//        privacyAgreementDTO.setOptIn(TriStateValue.YES);
//        privacyAgreementDTO.setTerms(TriStateValue.YES);
//
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .post(CASL_URL, privacyAgreementDTO, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then it should return the updated casl info
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.FALSE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.YES));
//        assertThat(privacy.getTerms(), is(TriStateValue.YES));
//        assertThat(privacy.getTimestamp(), is(notNullValue()));
//
//        //when: I request again the CASL information for the user
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .get(CASL_URL, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then: the updated CASL information should return as expected
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.FALSE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.YES));
//        assertThat(privacy.getTerms(), is(TriStateValue.YES));
//        assertThat(privacy.getTimestamp(), is(notNullValue()));
//
//        //when: I opt-out the Privacy
//        privacyAgreementDTO = new PrivacyAgreementDTO();
//        privacyAgreementDTO.setOptIn(TriStateValue.NO);
//        privacyAgreementDTO.setTerms(TriStateValue.NO);
//
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .post(CASL_URL, privacyAgreementDTO, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then: the updated Privacy information should return as expected
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.FALSE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.NO));
//        assertThat(privacy.getTerms(), is(TriStateValue.NO));
//        assertThat(privacy.getTimestamp(), is(notNullValue()));
//
//        //when: I request again the CASL information for the user
//        respPrivacy = securedAPI.withCredentials(BRITISH_USER.getEmail(), IOS_PLATFORM)
//                .get(CASL_URL, PrivacyDTO.class);
//        privacy = respPrivacy.getBody();
//
//        //then: the updated CASL information should return as expected
//        assertThat(respPrivacy.getStatusCode(), is(HttpStatus.OK));
//        assertThat(privacy.isRequired(), is(Boolean.FALSE));
//        assertThat(privacy.getOptIn(), is(TriStateValue.NO));
//        assertThat(privacy.getTerms(), is(TriStateValue.NO));
//        assertThat(privacy.getTimestamp(), is(notNullValue()));
//    }
}
