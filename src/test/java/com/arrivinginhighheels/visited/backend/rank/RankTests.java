package com.arrivinginhighheels.visited.backend.rank;

import com.arrivinginhighheels.visited.backend.dto.RankDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import com.arrivinginhighheels.visited.backend.utils.SelectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.RANK_URL;
import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNIN_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.BRAZIL_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.USA_KS_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.ADMIN_USER;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.model.SelectionType.BEEN;
import static com.arrivinginhighheels.visited.backend.model.SelectionType.LIVED;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the rank feature in all it's appearances in the API
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RankTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testRankRetuningOnSignin() {
        //given:
        Long numberOfUsers = SelectionUtils.clearAllUsersSelections(securedAPI);

        //when: user signs in
        var tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(REGULAR_USER.getEmail(), null, IOS_PLATFORM),
                        TokenResponseWithStats.class);
        RankDTO rank = tokenResponse.selections().getRank();

        //then:
        assertThat(rank, is(notNullValue()));
        assertThat(rank.getPosition(), is(1L)); //nobody has selections, so everyone is #1
        assertThat(rank.getTotalUsers(), is(numberOfUsers));
    }

    @Test
    public void testRankReturningOnSelection() {
        //given:
        Long numberOfUsers = SelectionUtils.clearAllUsersSelections(securedAPI);

        //when: user makes a selection
        SelectionsDTO selectionsDTO =
                SelectionUtils.selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, LIVED);
        RankDTO rank = selectionsDTO.getRank();

        //then: rank information should be in the selection returning DTO
        assertThat(rank, is(notNullValue()));
        assertThat(rank.getPosition(), is(1L)); //this user now has more selections than everyone else does
        assertThat(rank.getTotalUsers(), is(numberOfUsers));
    }

    @Test
    public void testDirectRankRestCall() {
        //given:
        Long numberOfUsers = SelectionUtils.clearAllUsersSelections(securedAPI);

        //when: user makes a selection
        SelectionsDTO selectionsDTO =
                SelectionUtils.selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, BEEN);

        //and: calls the RANK REST service
        ResponseEntity<RankDTO> response =
            securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                      .get(RANK_URL, RankDTO.class);

        //then:
        assertThat(response.getStatusCode(), is(HttpStatus.OK));
        RankDTO rank = response.getBody();
        assertThat(rank, is(notNullValue()));
        assertThat(rank.getPosition(), is(1L)); //this user now has more selections than everyone else does
        assertThat(rank.getTotalUsers(), is(numberOfUsers));
    }

    @Test
    public void testRankChangingOnMakingASelection() {
        //given:
        Long numberOfUsers = SelectionUtils.clearAllUsersSelections(securedAPI);

        //when: regular user signs in
        var tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(REGULAR_USER.getEmail(), null, IOS_PLATFORM),
                        TokenResponseWithStats.class);
        RankDTO rank = tokenResponse.selections().getRank();

        //then: rank should be #1
        assertThat(rank.getPosition(), is(1L));
        assertThat(rank.getTotalUsers(), is(numberOfUsers));

        //when: admin user signs in
        tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(ADMIN_USER.getEmail(), null,IOS_PLATFORM),
                        TokenResponseWithStats.class);
        rank = tokenResponse.selections().getRank();

        //then: rank should be also #1 as everyone is tied in #1
        assertThat(rank.getPosition(), is(1L));
        assertThat(rank.getTotalUsers(), is(numberOfUsers));

        //when: regular user selects some areas as lived or been
        SelectionUtils.selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, LIVED);
        SelectionsDTO selectionsDTO = SelectionUtils.selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, BEEN);
        rank = selectionsDTO.getRank();

        //then: regular user should still be #1
        assertThat(rank.getPosition(), is(1L)); //this user now has more selections than everyone else does
        assertThat(rank.getTotalUsers(), is(numberOfUsers));

        //when: admin user checks its rank
        ResponseEntity<RankDTO> response =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                          .get(RANK_URL, RankDTO.class);
        rank = response.getBody();

        //then: admin user should be #2, as the regular user now has more selections, but the cache will return #1
        // until recalculated the ranking via asynchronous job... not testing this here.
        assertThat(rank.getPosition(), is(1L));
        assertThat(rank.getTotalUsers(), is(numberOfUsers));
    }

}
