package com.arrivinginhighheels.visited.backend.utils;

import com.arrivinginhighheels.visited.backend.model.Platform;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import java.util.Collections;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNIN_URL;

/**
 * Fluent Interface based classes to facilitate the passing of credentials and authentication token to a Secured API
 * with JWT.
 */
public class SecureAPIRequestBuilder {

    private final TestRestTemplate restTemplate;

    private LoggedInRequestBuilder lastLoggedInRequestBuilder;

    public SecureAPIRequestBuilder(TestRestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public LoggedInRequestBuilder withCredentials(String email, Platform platform) {
        var tokenResponse = this.restTemplate
                .postForObject(SIGNIN_URL, new AuthRequest(email, null, platform), TokenResponse.class);

        if (tokenResponse == null || tokenResponse.token() == null) {
            throw new RuntimeException("Could not login with the provided credentials.");
        }

        var headers = new HttpHeaders();
        headers.put("Authorization", Collections.singletonList(tokenResponse.token()));

        lastLoggedInRequestBuilder = new LoggedInRequestBuilder(headers);
        return lastLoggedInRequestBuilder;
    }

    public LoggedInRequestBuilder withLoggedUser() {
        if (lastLoggedInRequestBuilder == null) {
            throw new RuntimeException("No credentials/token created. Call 'withCredentials' first.");
        }

        return lastLoggedInRequestBuilder;
    }

    public class LoggedInRequestBuilder {

        private final HttpHeaders headers;

        private LoggedInRequestBuilder(HttpHeaders headers) {
            this.headers = headers;
        }

        public LoggedInRequestBuilder setLanguage(String language) {
            headers.add(HttpHeaders.ACCEPT_LANGUAGE, language);
            return this;
        }

        public LoggedInRequestBuilder ifNoneMatch(String etag) {
            headers.add(HttpHeaders.IF_NONE_MATCH, etag);
            return this;
        }

        public <T, X> ResponseEntity<T> get(String url, Class<T> responseClass) {
            return restTemplate.exchange(url, HttpMethod.GET, getHttpEntity(null), responseClass);
        }

        public <T, X> ResponseEntity<T> post(String url, X requestObj, Class<T> responseClass) {
            return restTemplate.exchange(url, HttpMethod.POST, getHttpEntity(requestObj), responseClass);
        }

        public <T, X> ResponseEntity<T> put(String url, X requestObj, Class<T> responseClass) {
            return restTemplate.exchange(url, HttpMethod.PUT, getHttpEntity(requestObj), responseClass);
        }

        public <T, X> ResponseEntity<T> delete(String url, Class<T> responseClass) {
            return restTemplate.exchange(url, HttpMethod.DELETE, getHttpEntity(null), responseClass);
        }

        private <X> HttpEntity<X> getHttpEntity(X requestObj) {
            return new HttpEntity<X>(requestObj, headers);
        }
    }
}
